'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Stack,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Add,
} from '@mui/icons-material';
import { OnboardingCategory } from '@/types/onboarding';
import { useToast } from '@/app/providers/toast-provider';
import { categoriesApi } from '@/lib/api/categories';

const categorySchema = z.object({
  name: z.string().min(2, 'Category name must be at least 2 characters'),
  handle: z.string().min(2, 'Handle must be at least 2 characters').regex(/^[a-z0-9-]+$/, 'Handle can only contain lowercase letters, numbers, and hyphens'),
  description: z.string().min(5, 'Description must be at least 5 characters'),
  parentId: z.string().optional(),
});

type CategoryForm = z.infer<typeof categorySchema>;

interface CategoryManagementStepProps {
  onComplete: (data: { categories: OnboardingCategory[], subcategories: OnboardingCategory[] }) => void;
  onBack: () => void;
  storeHandle: string;
  initialData?: {
    categories: OnboardingCategory[];
    subcategories: OnboardingCategory[];
  };
}

interface ApiCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

type FlowStep = 'loading' | 'add-category' | 'add-subcategory' | 'complete';

export const CategoryManagementStep: React.FC<CategoryManagementStepProps> = ({
  onComplete,
  onBack,
  storeHandle,
  initialData
}) => {
  const [currentStep, setCurrentStep] = useState<FlowStep>('loading');
  const [existingCategories, setExistingCategories] = useState<ApiCategory[]>([]);
  const [existingSubcategories, setExistingSubcategories] = useState<ApiCategory[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset: resetForm,
    formState: { errors }
  } = useForm<CategoryForm>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      handle: '',
      description: '',
      parentId: '',
    }
  });

  const categoryName = watch('name');

  // Auto-generate handle from category name
  useEffect(() => {
    if (categoryName) {
      const handle = categoryName
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('handle', handle);
    }
  }, [categoryName, setValue]);

  // Fetch existing categories on component mount
  useEffect(() => {
    const fetchExistingCategories = async () => {
      setIsLoading(true);
      try {
        console.log('=== FETCHING EXISTING CATEGORIES ===');
        const result = await categoriesApi.getCategories();
        
        if (result.success && result.data) {
          console.log('Fetched categories:', result.data);
          
          // Separate categories and subcategories based on parent_category_id
          const categories = result.data.filter(cat => !cat.parent_category_id);
          const subcategories = result.data.filter(cat => cat.parent_category_id);
          
          console.log('Categories (no parent):', categories);
          console.log('Subcategories (with parent):', subcategories);
          
          setExistingCategories(categories);
          setExistingSubcategories(subcategories);
          
          // Determine the next step based on existing data
          if (categories.length === 0) {
            // No categories exist, need to add category first
            setCurrentStep('add-category');
          } else if (subcategories.length === 0) {
            // Categories exist but no subcategories, offer to add subcategory
            setCurrentStep('add-subcategory');
            // Pre-select the first category
            if (categories.length > 0) {
              setSelectedCategoryId(categories[0].id);
              setValue('parentId', categories[0].id);
            }
          } else {
            // Both categories and subcategories exist, can proceed to products
            setCurrentStep('complete');
          }
        } else {
          console.log('No categories found or API error:', result.error);
          // No categories found, start with adding category
          setCurrentStep('add-category');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        showToast('Failed to load existing categories', 'error');
        setCurrentStep('add-category');
      } finally {
        setIsLoading(false);
      }
    };

    fetchExistingCategories();
  }, [setValue, showToast]);

  const onSubmit = async (data: CategoryForm) => {
    setIsSubmitting(true);
    
    try {
      // Prepare API payload
      const payload = {
        name: data.name,
        handle: data.handle,
        description: data.description,
        ...(currentStep === 'add-subcategory' && data.parentId && { parent_category_id: data.parentId }),
      };

      console.log('Creating category/subcategory with payload:', payload);
      
      const result = await categoriesApi.createCategory(payload);

      if (result.success) {
        const isCategory = currentStep === 'add-category';
        showToast(
          `${isCategory ? 'Category' : 'Subcategory'} created successfully!`, 
          'success'
        );
        
        // Reset form
        resetForm();
        
        if (isCategory) {
          // After creating category, move to subcategory step
          // Refresh categories list
          const updatedResult = await categoriesApi.getCategories();
          if (updatedResult.success && updatedResult.data) {
            const categories = updatedResult.data.filter(cat => !cat.parent_category_id);
            setExistingCategories(categories);
            
            // Pre-select the newly created category
            const newCategory = categories.find(cat => cat.name === data.name);
            if (newCategory) {
              setSelectedCategoryId(newCategory.id);
              setValue('parentId', newCategory.id);
            }
          }
          setCurrentStep('add-subcategory');
        } else {
          // After creating subcategory, move to complete
          setCurrentStep('complete');
        }
      } else {
        showToast(result.error || 'Failed to create category', 'error');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      showToast('An error occurred while creating the category', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkipSubcategory = () => {
    setCurrentStep('complete');
  };

  const handleProceedToProducts = () => {
    // Convert API categories to OnboardingCategory format
    const categories: OnboardingCategory[] = existingCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      handle: cat.handle,
      description: cat.description,
      isSubcategory: false,
    }));

    const subcategories: OnboardingCategory[] = existingSubcategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      handle: cat.handle,
      description: cat.description,
      parentId: cat.parent_category_id,
      isSubcategory: true,
    }));

    onComplete({ categories, subcategories });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 6 }}>
        <Stack alignItems="center" spacing={2}>
          <CircularProgress />
          <Typography color="text.secondary">
            Loading existing categories...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (
    <Box>
      <Stack spacing={4} sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
            Category Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {currentStep === 'add-category' && 'Create a category for your products to get started.'}
            {currentStep === 'add-subcategory' && 'Add a subcategory to better organize your products within the category.'}
            {currentStep === 'complete' && 'Your categories are set up! You can now proceed to add products.'}
          </Typography>
        </Box>
      </Stack>

      <Stack direction={{ xs: 'column', lg: 'row' }} spacing={4}>
        {/* Form Section */}
        <Box sx={{ flex: 1 }}>
          <Card sx={{ bgcolor: 'grey.50' }}>
            <CardContent>
              {currentStep === 'add-category' && (
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                      Create Category
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Add a main category for your products
                    </Typography>
                  </Box>

                  <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <TextField
                      label="Category Name"
                      {...register('name')}
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      placeholder="Enter category name"
                      required
                      fullWidth
                    />

                    <TextField
                      label="Handle"
                      {...register('handle')}
                      error={!!errors.handle}
                      helperText={errors.handle?.message || `URL: /${storeHandle}/category/${watch('handle')}`}
                      placeholder="category-handle"
                      required
                      fullWidth
                    />

                    <TextField
                      label="Description"
                      {...register('description')}
                      error={!!errors.description}
                      helperText={errors.description?.message}
                      placeholder="Describe this category..."
                      multiline
                      rows={3}
                      required
                      fullWidth
                    />

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      variant="contained"
                      fullWidth
                      size="large"
                      startIcon={isSubmitting ? <CircularProgress size={16} /> : <Add />}
                    >
                      {isSubmitting ? 'Creating Category...' : 'Create Category'}
                    </Button>
                  </Box>
                </Stack>
              )}

          {currentStep === 'add-subcategory' && (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Create Subcategory</h3>
                <p className="text-sm text-gray-600 mt-1">Add a subcategory under your main category</p>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label htmlFor="parent-category" className="block text-sm font-medium text-gray-700 mb-2">
                    Parent Category *
                  </label>
                  <select
                    id="parent-category"
                    {...register('parentId', { required: 'Please select a parent category' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select parent category</option>
                    {existingCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  {errors.parentId && (
                    <p className="text-red-500 text-sm mt-1">{errors.parentId.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="subcategory-name" className="block text-sm font-medium text-gray-700 mb-2">
                    Subcategory Name *
                  </label>
                  <input
                    id="subcategory-name"
                    type="text"
                    {...register('name')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter subcategory name"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="subcategory-handle" className="block text-sm font-medium text-gray-700 mb-2">
                    Handle *
                  </label>
                  <input
                    id="subcategory-handle"
                    type="text"
                    {...register('handle')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="subcategory-handle"
                  />
                  {errors.handle && (
                    <p className="text-red-500 text-sm mt-1">{errors.handle.message}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    URL: /{storeHandle}/subcategory/{watch('handle')}
                  </p>
                </div>

                <div>
                  <label htmlFor="subcategory-description" className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    id="subcategory-description"
                    {...register('description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe this subcategory..."
                  />
                  {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
                  )}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`flex-1 py-2 px-4 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      isSubmitting
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
                    }`}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Creating...</span>
                      </div>
                    ) : (
                      'Create Subcategory'
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleSkipSubcategory}
                    className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Skip Subcategory
                  </button>
                </div>
              </form>
            </>
          )}

          {currentStep === 'complete' && (
            <div className="text-center py-8">
              <div className="text-green-600 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Categories Setup Complete!</h3>
              <p className="text-gray-600 mb-6">You can now proceed to add products to your categories.</p>
              <button
                onClick={handleProceedToProducts}
                className="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700"
              >
                Continue to Products
              </button>
            </div>
          )}
            </CardContent>
          </Card>
        </Box>

        {/* Summary Section */}
        <Box sx={{ flex: 1 }}>
          <Stack spacing={3}>
          {/* Existing Categories */}
          {existingCategories.length > 0 && (
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Categories ({existingCategories.length})
              </h3>
              <div className="space-y-3">
                {existingCategories.map((category) => (
                  <div key={category.id} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-gray-900">{category.name}</h5>
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                        Added
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                    <p className="text-xs text-gray-500 mt-1">Handle: {category.handle}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Existing Subcategories */}
          {existingSubcategories.length > 0 && (
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Subcategories ({existingSubcategories.length})
              </h3>
              <div className="space-y-3">
                {existingSubcategories.map((subcategory) => {
                  const parentCategory = existingCategories.find(cat => cat.id === subcategory.parent_category_id);
                  return (
                    <div key={subcategory.id} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium text-gray-900">{subcategory.name}</h5>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          Added
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{subcategory.description}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Parent: {parentCategory?.name} | Handle: {subcategory.handle}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
            <div className="text-sm text-blue-800 space-y-1">
              {currentStep === 'add-category' && (
                <p>Create at least one category to organize your products.</p>
              )}
              {currentStep === 'add-subcategory' && (
                <p>Optionally add subcategories for better product organization, or skip to proceed.</p>
              )}
              {currentStep === 'complete' && (
                <p>Your categories are ready! Click "Continue to Products" to start adding products.</p>
              )}
            </div>
          </div>
          </Stack>
        </Box>
      </Stack>

      {/* Navigation Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ pt: 4, mt: 4, borderTop: 1, borderColor: 'divider' }}>
        <Button
          onClick={onBack}
          variant="outlined"
          startIcon={<ArrowBack />}
          size="large"
        >
          Back to Store Details
        </Button>

        {currentStep === 'complete' && (
          <Button
            onClick={handleProceedToProducts}
            variant="contained"
            endIcon={<ArrowForward />}
            size="large"
          >
            Continue to Products
          </Button>
        )}
      </Stack>
    </Box>
  );
};