'use client';

import React from 'react';
import { Order } from '@/lib/api/medusa/orders';

interface OrderCardProps {
  order: Order;
  storeHandle: string;
}

export const OrderCard: React.FC<OrderCardProps> = ({ order, storeHandle }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (amount: number | undefined | null, currencyCode: string | undefined | null) => {
    if (amount === undefined || amount === null || !currencyCode) {
      return '$0.00';
    }
    
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode.toUpperCase(),
      }).format(amount / 100); // Assuming amount is in cents
    } catch (error) {
      console.warn('Error formatting price:', error);
      return `${amount / 100} ${currencyCode}`;
    }
  };

  const getStatusColor = (status: string | undefined | null) => {
    if (!status) return 'text-gray-600 bg-gray-50';
    
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getFulfillmentStatusColor = (status: string | undefined | null) => {
    if (!status) return 'text-gray-600 bg-gray-50';
    
    switch (status.toLowerCase()) {
      case 'fulfilled':
        return 'text-green-600 bg-green-50';
      case 'shipped':
        return 'text-blue-600 bg-blue-50';
      case 'partially_fulfilled':
        return 'text-yellow-600 bg-yellow-50';
      case 'not_fulfilled':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div 
      className="bg-white rounded-lg border p-6 hover:shadow-md transition-shadow duration-200"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      {/* Order Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h3 
            className="text-lg font-semibold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Order #{order.display_id}
          </h3>
          <p 
            className="text-sm mt-1"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Placed on {formatDate(order.created_at)}
          </p>
        </div>
        <div className="flex flex-col sm:items-end mt-2 sm:mt-0">
          <p 
            className="text-lg font-semibold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {formatPrice(order.total, order.currency_code)}
          </p>
          <div className="flex gap-2 mt-1">
            <span 
              className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}
            >
              {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown'}
            </span>
            <span 
              className={`px-2 py-1 text-xs font-medium rounded-full ${getFulfillmentStatusColor(order.fulfillment_status)}`}
            >
              {order.fulfillment_status ? order.fulfillment_status.replace('_', ' ').charAt(0).toUpperCase() + order.fulfillment_status.replace('_', ' ').slice(1) : 'Unknown'}
            </span>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="space-y-3 mb-4">
        {order.items && order.items.length > 0 ? order.items.slice(0, 3).map((item) => (
          <div key={item.id} className="flex items-center space-x-3">
            {item.thumbnail && (
              <img
                src={item.thumbnail}
                alt={item.title}
                className="w-12 h-12 object-cover rounded-md"
                style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
              />
            )}
            <div className="flex-1 min-w-0">
              <p 
                className="text-sm font-medium truncate"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                {item.title}
              </p>
              <p 
                className="text-xs"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Qty: {item.quantity} × {formatPrice(item.unit_price, order.currency_code)}
              </p>
            </div>
            <p 
              className="text-sm font-medium"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              {formatPrice(item.total, order.currency_code)}
            </p>
          </div>
        )) : (
          <p 
            className="text-sm text-center py-4"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            No items found
          </p>
        )}
        {order.items && order.items.length > 3 && (
          <p 
            className="text-sm"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            +{order.items.length - 3} more items
          </p>
        )}
      </div>

      {/* Order Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t" style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}>
        <a
          href={`/${storeHandle}/orders/${order.display_id}`}
          className="flex-1 text-center px-4 py-2 border rounded-lg font-medium transition-colors duration-200 hover:bg-gray-50"
          style={{ 
            borderColor: 'var(--theme-border, #e5e7eb)',
            color: 'var(--theme-text, #111827)'
          }}
        >
          View Details
        </a>
        {order.status === 'completed' && order.fulfillment_status === 'fulfilled' && (
          <button
            className="flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            Reorder
          </button>
        )}
      </div>
    </div>
  );
};