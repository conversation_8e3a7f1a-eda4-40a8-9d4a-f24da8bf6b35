'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { useStoreAuthContext } from '@/components/auth/StoreAuthProvider';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { useOrders } from '@/hooks/medusa/useOrders';
import { OrderCard } from '@/components/order/OrderCard';

const OrdersPageContent: React.FC<{ storeHandle: string }> = ({ storeHandle }) => {
  const router = useRouter();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  const { isAuthenticated, isLoading: authLoading, user, token } = useStoreAuthContext();
  
  // Handle initial loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 500); // Small delay to show loading state
    
    return () => clearTimeout(timer);
  }, []);

  // Fetch orders using the API
  const { 
    data: ordersData, 
    isLoading: ordersLoading, 
    error: ordersError,
    refetch: refetchOrders
  } = useOrders(
    storeHandle,
    token || undefined,
    { limit: 20 },
    isAuthenticated && !!token
  );

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isInitialLoading && !authLoading && !isAuthenticated) {
      router.push(`/${storeHandle}`);
    }
  }, [isAuthenticated, authLoading, isInitialLoading, router, storeHandle]);

  if (storeLoading || authLoading || isInitialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p 
            className="mt-4 text-lg"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            {storeLoading ? 'Loading store...' : isInitialLoading ? 'Loading orders...' : 'Checking authentication...'}
          </p>
        </div>
      </div>
    );
  }

  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p 
            className="mt-4 text-lg"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Redirecting to home...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a 
                href={`/${storeHandle}`}
                className="transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Home
              </a>
            </li>
            <li>
              <svg className="w-4 h-4 mx-2" fill="none" stroke="var(--theme-text-secondary, #6b7280)" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </li>
            <li>
              <span style={{ color: 'var(--theme-text, #111827)' }} className="font-medium">
                My Orders
              </span>
            </li>
          </ol>
        </nav>

        {/* Page Title */}
        <div className="mb-8">
          <h1 
            className="text-3xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            My Orders
          </h1>
          <p 
            className="mt-2"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            View and track your order history
          </p>
        </div>

        {/* Orders Content */}
        {ordersLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : ordersError ? (
          <div className="bg-white rounded-lg shadow-sm border p-8" style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}>
            <ErrorMessage 
              message={ordersError.message || 'Failed to load orders'}
              onRetry={() => refetchOrders()}
            />
          </div>
        ) : ordersData && ordersData.orders && ordersData.orders.length > 0 ? (
          <div className="space-y-6">
            {/* Orders Summary */}
            <div className="bg-white rounded-lg shadow-sm border p-6" style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}>
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold" style={{ color: 'var(--theme-text, #111827)' }}>
                    Order History
                  </h2>
                  <p className="text-sm" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                    {ordersData.count} {ordersData.count === 1 ? 'order' : 'orders'} found
                  </p>
                </div>
                <button
                  onClick={() => refetchOrders()}
                  className="px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200 hover:bg-gray-50"
                  style={{ 
                    borderColor: 'var(--theme-border, #e5e7eb)',
                    color: 'var(--theme-text, #111827)'
                  }}
                >
                  Refresh
                </button>
              </div>
            </div>

            {/* Orders List */}
            <div className="grid gap-6">
              {ordersData.orders.map((order) => (
                <OrderCard 
                  key={order.id} 
                  order={order} 
                  storeHandle={storeHandle} 
                />
              ))}
            </div>

            {/* Load More */}
            {ordersData.orders.length < ordersData.count && (
              <div className="text-center pt-6">
                <button
                  className="px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
                  style={{ 
                    backgroundColor: 'var(--btn-secondary, #f3f4f6)',
                    color: 'var(--theme-text, #111827)',
                  }}
                >
                  Load More Orders
                </button>
              </div>
            )}
          </div>
        ) : (
          <div 
            className="bg-white rounded-lg shadow-sm border p-8"
            style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
          >
            <div className="text-center py-12">
              <svg 
                className="w-16 h-16 mx-auto mb-4 opacity-50"
                fill="none" 
                stroke="var(--theme-text-secondary)" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <h3 
                className="text-lg font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                No Orders Yet
              </h3>
              <p 
                className="text-sm mb-6"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                You haven't placed any orders yet. Start shopping to see your orders here.
              </p>
              <a
                href={`/${storeHandle}`}
                className="inline-flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
                style={{ 
                  backgroundColor: 'var(--btn-primary, #3b82f6)',
                  color: 'var(--btn-text, #ffffff)',
                }}
              >
                Start Shopping
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function OrdersPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <OrdersPageContent storeHandle={storeHandle} />
    </GlobalProviders>
  );
}