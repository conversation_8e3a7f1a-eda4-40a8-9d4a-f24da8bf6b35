import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { authenticate } from '@medusajs/framework';

/**
 * Store Orders Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered orders for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`📦 [STORE-ORDERS] Getting orders for authenticated customer`);
  console.log(`📦 [STORE-ORDERS] Headers:`, {
    authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
    'x-tenant-id': req.headers['x-tenant-id'],
    'x-publishable-api-key': req.headers['x-publishable-api-key']
  });

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log(`📦 [STORE-ORDERS] Processing orders request for tenant: ${tenantId}`);

    // Check if user is authenticated
    if (!req.auth_context || !req.auth_context.actor_id) {
      console.log(`❌ [STORE-ORDERS] No authentication context found`);
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access orders'
      });
    }

    const customerId = req.auth_context.actor_id;
    console.log(`📦 [STORE-ORDERS] Authenticated customer ID: ${customerId}`);

    // Get query parameters
    const { 
      limit = 50, 
      offset = 0, 
      fields, 
      order = '-created_at',
      ...filters 
    } = req.query;

    console.log(`📦 [STORE-ORDERS] Query params:`, { limit, offset, order, filters });

    // Use direct database connection for now to ensure proper tenant filtering
    const { Client } = require('pg');
    const client = new Client({
      connectionString: process.env.DATABASE_URL || 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let orders: any[] = [];
    let count = 0;

    try {
      await client.connect();
      console.log(`🔗 [STORE-ORDERS] Connected to database`);

      // Get total count for this customer and tenant
      const countResult = await client.query(
        `SELECT COUNT(*) as total 
         FROM "order" 
         WHERE customer_id = $1 
         AND tenant_id = $2 
         AND deleted_at IS NULL`,
        [customerId, tenantId]
      );
      count = parseInt(countResult.rows[0]?.total || 0);
      console.log(`📊 [STORE-ORDERS] Total orders for customer ${customerId} in tenant ${tenantId}: ${count}`);

      if (count === 0) {
        console.log(`📦 [STORE-ORDERS] No orders found for customer`);
        return res.status(200).json({
          orders: [],
          count: 0,
          offset: parseInt(offset as string),
          limit: parseInt(limit as string)
        });
      }

      // Get orders with pagination
      const result = await client.query(
        `
        SELECT 
          id, status, currency_code, email, display_id,
          created_at, updated_at, tenant_id, metadata,
          customer_id, region_id, sales_channel_id,
          shipping_address_id, billing_address_id,
          is_draft_order, no_notification
        FROM "order" 
        WHERE customer_id = $1 
        AND tenant_id = $2 
        AND deleted_at IS NULL
        ORDER BY created_at DESC 
        LIMIT $3 OFFSET $4
      `,
        [customerId, tenantId, parseInt(limit as string), parseInt(offset as string)]
      );

      orders = result.rows;
      console.log(`✅ [STORE-ORDERS] Found ${orders.length} orders for customer`);

      // If we need to populate relations, we can do additional queries here
      // For now, return basic order information

    } catch (dbError) {
      console.error('❌ [STORE-ORDERS] Database error:', dbError);
      throw dbError;
    } finally {
      await client.end();
    }

    // Return response in Medusa v2 format
    const response = {
      orders,
      count,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string)
    };

    console.log(`✅ [STORE-ORDERS] Returning ${orders.length} orders`);
    return res.status(200).json(response);

  } catch (error) {
    console.error('❌ [STORE-ORDERS] Error fetching orders:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch orders'
    });
  }
}
